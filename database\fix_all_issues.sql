-- Fix All Database Issues Script
-- Run this script in Supabase SQL Editor to fix all identified issues
-- This script addresses RLS policy infinite recursion and missing policies

-- Step 1: Drop existing problematic policies
DROP POLICY IF EXISTS "Users can view class members of their classes" ON public.class_members;
DROP POLICY IF EXISTS "Users can view assignments in their classes" ON public.assignments;
DROP POLICY IF EXISTS "Teachers can add themselves to their classes" ON public.class_members;

-- Step 2: Create fixed RLS policies for class_members table
-- Fix infinite recursion issue by avoiding self-reference
CREATE POLICY "Users can view class members of their classes" ON public.class_members
  FOR SELECT USING (
    -- Allow users to see their own membership record
    user_id = auth.uid() OR
    -- Allow teachers to see all members of their classes
    EXISTS (
      SELECT 1 FROM public.classes c
      WHERE c.id = class_id AND c.teacher_id = auth.uid()
    )
  );

-- Step 3: Add missing policy for teachers to add themselves to their classes
CREATE POLICY "Teachers can add themselves to their classes" ON public.class_members
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND role = 'teacher' AND
    EXISTS (
      SELECT 1 FROM public.classes 
      WHERE id = class_id AND teacher_id = auth.uid()
    )
  );

-- Step 4: Fix assignments RLS policy to avoid circular reference
CREATE POLICY "Users can view assignments in their classes" ON public.assignments
  FOR SELECT USING (
    -- Teachers can see assignments in their classes
    EXISTS (
      SELECT 1 FROM public.classes c
      WHERE c.id = class_id AND c.teacher_id = auth.uid()
    ) OR
    -- Students can see assignments in classes they're members of
    EXISTS (
      SELECT 1 FROM public.class_members cm
      WHERE cm.class_id = class_id AND cm.user_id = auth.uid() AND cm.role = 'student'
    )
  );

-- Step 5: Verify all tables have RLS enabled
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.class_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Step 6: Create storage bucket for avatars (if using Supabase storage)
-- Note: This is optional since the app now uses Cloudinary for avatars
-- INSERT INTO storage.buckets (id, name, public) VALUES ('avatars', 'avatars', true);

-- Step 7: Success message
SELECT 'All database issues have been fixed successfully!' as message,
       'RLS policies updated to prevent infinite recursion' as fix_1,
       'Missing teacher self-assignment policy added' as fix_2,
       'Assignment view policy fixed' as fix_3;
